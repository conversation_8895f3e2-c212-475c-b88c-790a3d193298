<!-- Game Detail Page -->
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-950 to-gray-900">
  <!-- Header -->
  <app-header></app-header>

  <!-- Main Content -->
  <div class="pt-20 pb-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      
      <!-- Back Button -->
      <div class="mb-6">
        <button
          (click)="goBack()"
          class="flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          Назад к каталогу
        </button>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="flex justify-center items-center py-20">
        <div class="bg-slate-800/60 backdrop-blur-md border border-purple-400/30 rounded-xl p-6 shadow-2xl">
          <svg class="animate-spin h-10 w-10 text-purple-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="error && !loading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-6 text-center">
        <p class="text-red-300 mb-4">{{ error }}</p>
        <button 
          (click)="loadGame()" 
          class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
        >
          Попробовать снова
        </button>
      </div>

      <!-- Game Content -->
      <div *ngIf="game && !loading && !error">
        <!-- Game Header -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          <!-- Cover Image -->
          <div class="lg:col-span-1">
            <div class="aspect-square bg-slate-800/40 border border-slate-600/50 rounded-lg overflow-hidden">
              <img 
                *ngIf="game.cover_image" 
                [src]="game.cover_image" 
                [alt]="game.title"
                class="w-full h-full object-cover">
              <div *ngIf="!game.cover_image" class="w-full h-full flex items-center justify-center text-gray-400">
                <svg class="w-24 h-24" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                </svg>
              </div>
            </div>
          </div>

          <!-- Game Info -->
          <div class="lg:col-span-2 space-y-4">
            <div>
              <h1 class="text-2xl md:text-3xl font-bold text-white mb-3">{{ game.title }}</h1>
              <p *ngIf="game.subtitle" class="text-lg text-gray-300 mb-4">{{ game.subtitle }}</p>

              <!-- Price and Badges -->
              <div class="flex flex-wrap items-center gap-3 mb-4">
                <span class="text-xl font-bold text-green-400">{{ formatPrice(game.price) }}</span>
                <span *ngIf="game.trial_available" class="px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
                  Пробная версия доступна
                </span>
                <span *ngIf="game.requires_device" class="px-2 py-1 bg-orange-600 text-white text-xs rounded-full">
                  Требует специальное устройство
                </span>
              </div>

              <!-- Actions -->
              <div class="flex gap-3 items-center">
                <!-- Add to Cart Button (when not in library or cart) -->
                <button
                  *ngIf="!isInLibrary() && !isInCart()"
                  (click)="addToCart()"
                  class="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg transition-all transform hover:scale-105 text-sm font-medium shadow-lg"
                >
                  Добавить в корзину
                </button>

                <!-- Already in Cart Button -->
                <button
                  *ngIf="!isInLibrary() && isInCart()"
                  disabled
                  class="px-4 py-2 bg-gray-600 text-gray-300 rounded-lg cursor-not-allowed text-sm font-medium"
                >
                  Уже в корзине
                </button>

                <!-- Already in Library - Beautiful Text Badge with Access Info -->
                <div
                  *ngIf="isInLibrary()"
                  class="space-y-2"
                >
                  <!-- Library Status -->
                  <div class="flex items-center gap-2 px-3 py-1 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-lg">
                    <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-green-400 font-medium text-xs">Уже в вашей библиотеке</span>
                  </div>

                  <!-- Access Information -->
                  <div *ngIf="game?.access_end" class="px-3 py-2 bg-slate-700/40 border border-slate-600/50 rounded-lg">
                    <div *ngIf="hasActiveAccess()" class="space-y-1">
                      <div class="flex items-center gap-2">
                        <svg class="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-blue-400 font-medium text-xs">Доступ активен</span>
                      </div>
                      <p class="text-gray-300 text-xs">Осталось: {{ getRemainingTime() }}</p>
                      <p class="text-gray-400 text-xs">До: {{ getAccessEndDate() }}</p>
                    </div>
                    <div *ngIf="!hasActiveAccess()" class="space-y-1">
                      <div class="flex items-center gap-2">
                        <svg class="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <span class="text-red-400 font-medium text-xs">Доступ истёк</span>
                      </div>
                      <p class="text-gray-400 text-xs">Истёк: {{ getAccessEndDate() }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Creation Date -->
              <p class="text-gray-400 text-xs mt-3">
                Добавлено: {{ formatDate(game.created_at) }}
              </p>
            </div>
          </div>
        </div>

        <!-- Game Details Sections -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Description -->
          <div class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-4">
            <h2 class="text-lg font-bold text-white mb-3">Описание</h2>
            <p class="text-gray-300 leading-relaxed text-sm">{{ game.description }}</p>
          </div>

          <!-- How to Play -->
          <div *ngIf="game.how_to_play" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-4">
            <h2 class="text-lg font-bold text-white mb-3">Как играть</h2>
            <p class="text-gray-300 leading-relaxed text-sm">{{ game.how_to_play }}</p>
          </div>

          <!-- Target Audience -->
          <div *ngIf="game.target_audience" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-4">
            <h2 class="text-lg font-bold text-white mb-3">Целевая аудитория</h2>
            <p class="text-gray-300 leading-relaxed text-sm">{{ game.target_audience }}</p>
          </div>

          <!-- System Requirements -->
          <div *ngIf="game.system_requirements" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-4">
            <h2 class="text-lg font-bold text-white mb-3">Системные требования</h2>
            <p class="text-gray-300 leading-relaxed text-sm">{{ game.system_requirements }}</p>
          </div>

          <!-- Required Equipment -->
          <div *ngIf="game.required_equipment" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-4">
            <h2 class="text-lg font-bold text-white mb-3">Необходимое оборудование</h2>
            <p class="text-gray-300 leading-relaxed text-sm">{{ game.required_equipment }}</p>
          </div>
        </div>

        <!-- Beautiful Gallery -->
        <div *ngIf="game.gallery_items && game.gallery_items.length > 0" class="mt-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-bold text-white flex items-center">
              <svg class="w-5 h-5 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              Галерея
            </h2>
            <span class="text-gray-400 text-xs bg-slate-800/60 px-2 py-1 rounded-full">
              {{ game.gallery_items.length }} {{ game.gallery_items.length === 1 ? 'изображение' : 'изображений' }}
            </span>
          </div>

          <!-- Main Gallery Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            <div
              *ngFor="let item of game.gallery_items; let i = index"
              (click)="openGallery(i)"
              class="group relative aspect-video bg-gradient-to-br from-slate-800/60 to-slate-900/60 border border-slate-600/50 rounded-xl overflow-hidden cursor-pointer transform transition-all duration-300 hover:scale-[1.02] hover:border-blue-500/50 hover:shadow-xl hover:shadow-blue-500/20">

              <!-- Image -->
              <img
                [src]="item.file"
                [alt]="game.title + ' gallery image ' + (i + 1)"
                class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                loading="lazy">

              <!-- Overlay -->
              <div class="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-all duration-300 flex items-center justify-center">
                <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform scale-75 group-hover:scale-100">
                  <div class="bg-white/20 backdrop-blur-sm rounded-full p-3">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Image Number Badge -->
              <div class="absolute top-3 right-3 bg-black/60 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                {{ i + 1 }} / {{ game.gallery_items.length }}
              </div>
            </div>
          </div>

          <!-- Gallery Info -->
          <div class="mt-3 text-center">
            <p class="text-gray-400 text-xs">Нажмите на изображение для просмотра в полном размере</p>
          </div>
        </div>

        <!-- Beautiful Conclusion Section -->
        <div class="mt-8 bg-gradient-to-br from-slate-800/60 via-blue-900/20 to-purple-900/20 border border-slate-600/30 rounded-2xl p-6 backdrop-blur-sm">
          <div class="text-center space-y-4">
            <!-- Conclusion Header -->
            <div class="space-y-2">
              <div class="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-3">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h3 class="text-xl md:text-2xl font-bold text-white">
                Готовы начать играть в {{ game.title }}?
              </h3>
              <p class="text-gray-300 text-sm max-w-2xl mx-auto leading-relaxed">
                Эта увлекательная игра станет отличным дополнением к вашему мероприятию и подарит незабываемые эмоции всем участникам.
              </p>
            </div>

            <!-- Game Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mt-6">
              <!-- Price Card -->
              <div class="bg-slate-700/40 border border-slate-600/50 rounded-xl p-3">
                <div class="text-center">
                  <div class="text-lg font-bold text-green-400 mb-1">{{ formatPrice(game.price) }}</div>
                  <div class="text-xs text-gray-400">Стоимость игры</div>
                </div>
              </div>

              <!-- Trial Card -->
              <div class="bg-slate-700/40 border border-slate-600/50 rounded-xl p-3">
                <div class="text-center">
                  <div class="text-sm font-semibold mb-1" [class]="game.trial_available ? 'text-blue-400' : 'text-gray-400'">
                    {{ game.trial_available ? 'Доступна' : 'Недоступна' }}
                  </div>
                  <div class="text-xs text-gray-400">Пробная версия</div>
                </div>
              </div>

              <!-- Device Card -->
              <div class="bg-slate-700/40 border border-slate-600/50 rounded-xl p-3">
                <div class="text-center">
                  <div class="text-sm font-semibold mb-1" [class]="game.requires_device ? 'text-orange-400' : 'text-green-400'">
                    {{ game.requires_device ? 'Требуется' : 'Не требуется' }}
                  </div>
                  <div class="text-xs text-gray-400">Спец. устройство</div>
                </div>
              </div>
            </div>

            <!-- Action Section -->
            <div class="mt-6 pt-4 border-t border-slate-600/30">
              <div class="flex flex-col sm:flex-row gap-3 justify-center items-center">
                <!-- Main Action Button -->
                <div *ngIf="!isInLibrary()">
                  <button
                    *ngIf="!isInCart()"
                    (click)="addToCart()"
                    class="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl transition-all transform hover:scale-105 text-sm font-semibold shadow-xl hover:shadow-2xl"
                  >
                    <span class="flex items-center gap-2">
                      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                      </svg>
                      Добавить в корзину
                    </span>
                  </button>

                  <div
                    *ngIf="isInCart()"
                    class="px-6 py-3 bg-slate-600/50 border border-slate-500/50 text-slate-300 rounded-xl text-sm font-medium"
                  >
                    <span class="flex items-center gap-2">
                      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                      Уже в корзине
                    </span>
                  </div>
                </div>

                <!-- Library Status with Access Info -->
                <div
                  *ngIf="isInLibrary()"
                  class="space-y-3"
                >
                  <!-- Main Library Status -->
                  <div class="px-6 py-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/40 rounded-xl">
                    <span class="flex items-center gap-2 text-green-400 text-sm font-semibold">
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      Игра уже в вашей библиотеке
                    </span>
                  </div>

                  <!-- Access Information Card -->
                  <div *ngIf="game?.access_end" class="px-6 py-4 bg-slate-700/40 border border-slate-600/50 rounded-xl">
                    <div *ngIf="hasActiveAccess()" class="text-center space-y-2">
                      <div class="flex items-center justify-center gap-2 mb-2">
                        <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-blue-400 font-semibold">Доступ активен</span>
                      </div>
                      <div class="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3">
                        <p class="text-white font-medium">Осталось: {{ getRemainingTime() }}</p>
                        <p class="text-gray-300 text-sm mt-1">Доступ до: {{ getAccessEndDate() }}</p>
                      </div>
                    </div>
                    <div *ngIf="!hasActiveAccess()" class="text-center space-y-2">
                      <div class="flex items-center justify-center gap-2 mb-2">
                        <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <span class="text-red-400 font-semibold">Доступ истёк</span>
                      </div>
                      <div class="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
                        <p class="text-red-300 font-medium">Доступ истёк</p>
                        <p class="text-gray-400 text-sm mt-1">Истёк: {{ getAccessEndDate() }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Back to Games Button -->
                <button
                  (click)="goBack()"
                  class="px-4 py-2 bg-slate-700/50 hover:bg-slate-600/50 border border-slate-600/50 hover:border-slate-500/50 text-gray-300 hover:text-white rounded-xl transition-all text-sm font-medium"
                >
                  <span class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Назад к играм
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Simple Gallery Modal -->
<div
  *ngIf="showGalleryModal"
  class="fixed inset-0 z-50 flex items-center justify-center bg-black/90"
  (click)="closeGallery()">

  <!-- Modal Content -->
  <div class="relative w-full h-full flex items-center justify-center p-4" (click)="$event.stopPropagation()">

    <!-- Close Button -->
    <button
      (click)="closeGallery()"
      class="absolute top-6 right-6 z-10 bg-black/70 hover:bg-black/90 text-white rounded-full p-3 transition-colors">
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>

    <!-- Previous Button -->
    <button
      *ngIf="galleryImages.length > 1"
      (click)="previousImage()"
      class="absolute left-6 top-1/2 transform -translate-y-1/2 z-10 bg-black/70 hover:bg-black/90 text-white rounded-full p-4 transition-colors">
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
      </svg>
    </button>

    <!-- Next Button -->
    <button
      *ngIf="galleryImages.length > 1"
      (click)="nextImage()"
      class="absolute right-6 top-1/2 transform -translate-y-1/2 z-10 bg-black/70 hover:bg-black/90 text-white rounded-full p-4 transition-colors">
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
      </svg>
    </button>

    <!-- Main Image -->
    <div class="relative">
      <img
        [src]="galleryImages[currentImageIndex]"
        [alt]="game?.title + ' gallery image ' + (currentImageIndex + 1)"
        class="max-w-[90vw] max-h-[80vh] object-contain rounded-lg shadow-2xl">

      <!-- Image Counter -->
      <div *ngIf="galleryImages.length > 1" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
        {{ currentImageIndex + 1 }} / {{ galleryImages.length }}
      </div>
    </div>
  </div>
</div>
