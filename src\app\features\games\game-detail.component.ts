import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { GameService } from '../../core/services/game.service';
import { CartService } from '../../core/services/cart.service';
import { ModalService } from '../../core/services/modal.service';
import { Game } from '../../core/models/game.model';

@Component({
  selector: 'app-game-detail',
  standalone: false,
  templateUrl: './game-detail.component.html',
  styleUrl: './game-detail.component.css'
})
export class GameDetailComponent implements OnInit, OnDestroy {
  game: Game | null = null;
  loading = false;
  error = '';
  gameId: number | null = null;

  // Gallery
  showGalleryModal = false;
  currentImageIndex = 0;
  galleryImages: string[] = [];
  private keydownListener?: (event: KeyboardEvent) => void;

  // Cart
  cartItemCount = 0;
  private cartSubscription?: Subscription;
  private cartChangesSubscription?: Subscription;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private gameService: GameService,
    private cartService: CartService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.gameId = +params['id'];
      if (this.gameId) {
        this.loadGame();
      }
    });

    this.setupCartSubscription();
    this.setupCartChangesSubscription();
  }

  ngOnDestroy(): void {
    this.cartSubscription?.unsubscribe();
    this.cartChangesSubscription?.unsubscribe();

    // Clean up gallery modal if open
    if (this.showGalleryModal) {
      this.closeGallery();
    }
  }

  private setupCartSubscription(): void {
    this.cartSubscription = this.cartService.cart$.subscribe(cart => {
      this.cartItemCount = cart.total_items;
    });
  }

  private setupCartChangesSubscription(): void {
    this.cartChangesSubscription = this.cartService.cartChanges$.subscribe(change => {
      // Update the current game's is_in_cart status if it matches
      if (this.game && this.game.id === change.gameId) {
        this.game.is_in_cart = change.action === 'added';
      }
    });
  }

  loadGame(): void {
    if (!this.gameId) return;

    this.loading = true;
    this.error = '';

    this.gameService.getGame(this.gameId).subscribe({
      next: (game) => {
        this.game = game;
        this.setupGallery();
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить информацию об игре';
        this.loading = false;
      }
    });
  }

  private setupGallery(): void {
    if (this.game?.gallery_items) {
      this.galleryImages = this.game.gallery_items.map(item => item.file);
    }
  }

  openGallery(index: number): void {
    this.currentImageIndex = index;
    this.showGalleryModal = true;
    document.body.style.overflow = 'hidden';

    // Add keyboard event listener
    this.keydownListener = (event: KeyboardEvent) => this.handleKeyPress(event);
    document.addEventListener('keydown', this.keydownListener);
  }

  closeGallery(): void {
    this.showGalleryModal = false;
    document.body.style.overflow = 'auto';

    // Remove keyboard event listener
    if (this.keydownListener) {
      document.removeEventListener('keydown', this.keydownListener);
      this.keydownListener = undefined;
    }
  }

  private handleKeyPress(event: KeyboardEvent): void {
    if (!this.showGalleryModal) return;

    switch (event.key) {
      case 'Escape':
        this.closeGallery();
        break;
      case 'ArrowLeft':
        this.previousImage();
        break;
      case 'ArrowRight':
        this.nextImage();
        break;
    }
  }

  nextImage(): void {
    if (this.currentImageIndex < this.galleryImages.length - 1) {
      this.currentImageIndex++;
    } else {
      this.currentImageIndex = 0; // Loop to first image
    }
  }

  previousImage(): void {
    if (this.currentImageIndex > 0) {
      this.currentImageIndex--;
    } else {
      this.currentImageIndex = this.galleryImages.length - 1; // Loop to last image
    }
  }

  goToImage(index: number): void {
    this.currentImageIndex = index;
  }

  addToCart(): void {
    if (this.game) {
      if (this.isInLibrary()) {
        this.modalService.error('Игра уже в библиотеке', 'Эта игра уже есть в вашей библиотеке');
        return;
      }

      this.cartService.addToCart(this.game).subscribe({
        next: () => {
          // Success - cart will be automatically updated via subscription
          // Update the local game object to reflect the cart status immediately
          if (this.game) {
            this.game.is_in_cart = true;
          }
          console.log('Game added to cart successfully');
        },
        error: (error) => {
          console.error('Error adding game to cart:', error.message);
          this.modalService.error('Ошибка', 'Не удалось добавить игру в корзину: ' + error.message);
        }
      });
    }
  }

  isInCart(): boolean {
    if (!this.game) return false;

    // Use the is_in_cart field from the API response if available (when user is authenticated)
    // Fall back to cart service check if not available (when user is not authenticated)
    return this.game.is_in_cart !== undefined ? this.game.is_in_cart : this.cartService.isInCart(this.game.id);
  }

  isInLibrary(): boolean {
    if (!this.game) return false;
    return this.game.is_in_library || false;
  }

  goBack(): void {
    this.router.navigate(['/games']);
  }

  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }

  hasActiveAccess(): boolean {
    if (!this.game || !this.game.access_end) return false;
    const accessEnd = new Date(this.game.access_end);
    return accessEnd > new Date();
  }

  getAccessEndDate(): string {
    if (!this.game?.access_end) return '';
    const accessEnd = new Date(this.game.access_end);
    return accessEnd.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getRemainingTime(): string {
    if (!this.game?.access_end) return '';
    const accessEnd = new Date(this.game.access_end);
    const now = new Date();
    const diff = accessEnd.getTime() - now.getTime();

    if (diff <= 0) return 'Доступ истёк';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days} ${days === 1 ? 'день' : days < 5 ? 'дня' : 'дней'}`;
    } else if (hours > 0) {
      return `${hours} ${hours === 1 ? 'час' : hours < 5 ? 'часа' : 'часов'}`;
    } else {
      return `${minutes} ${minutes === 1 ? 'минута' : minutes < 5 ? 'минуты' : 'минут'}`;
    }
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}
