export interface GameGalleryItem {
  id: number;
  game: number;
  file: string;
  uploaded_at: string;
}

export interface Game {
  id: number;
  title: string;
  subtitle?: string;
  description: string;
  how_to_play?: string;
  target_audience?: string;
  requires_device: boolean;
  price: string;
  trial_available: boolean;
  cover_image: string | null;
  gallery_items?: GameGalleryItem[];
  system_requirements?: string;
  required_equipment?: string;
  created_at: string;
  is_in_cart?: boolean; // Available when user is authenticated
  is_in_library?: boolean; // Available when user is authenticated
  access_end?: string | null; // Available when user is authenticated and game is in library
}

export interface CreateGameRequest {
  title: string;
  subtitle?: string;
  description: string;
  how_to_play?: string;
  target_audience?: string;
  requires_device: boolean;
  price: string;
  trial_available: boolean;
  cover_image?: File | string;
  system_requirements?: string;
  required_equipment?: string;
}

export interface CreateGalleryItemRequest {
  game: number;
  file: File;
}

export interface UpdateGameRequest extends Partial<CreateGameRequest> {}

export interface GameFilters {
  ordering?: string;
  search?: string;
}

export interface GameListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Game[];
}

export interface GameError {
  title?: string[];
  subtitle?: string[];
  description?: string[];
  how_to_play?: string[];
  target_audience?: string[];
  requires_device?: string[];
  price?: string[];
  trial_available?: string[];
  cover_image?: string[];
  gallery_images?: string[];
  system_requirements?: string[];
  required_equipment?: string[];
  non_field_errors?: string[];
}
